import { BaseError } from './base-error'
import type { BaseErrorOptions } from './types'

export class ValidationError extends BaseError {
    public readonly field?: string

    public constructor(message: string, options?: BaseErrorOptions & { field?: string }) {
        const { field, ...baseOptions } = options ?? {}

        super(message, { ...baseOptions, name: 'ValidationError' })

        this.field = field
    }
}

export class TimeoutError extends BaseError {
    public readonly timeout: number

    public constructor(message: string, timeout: number, options?: BaseErrorOptions) {
        super(message, { ...options, name: 'TimeoutError' })

        this.timeout = timeout
    }
}

export class NotFoundError extends BaseError {
    public readonly resource?: string

    public constructor(message: string, options?: BaseErrorOptions & { resource?: string }) {
        const { resource, ...baseOptions } = options ?? {}

        super(message, { ...baseOptions, name: 'NotFoundError', code: 404 })

        this.resource = resource
    }
}

export class ConfigurationError extends BaseError {
    public readonly config?: string

    public constructor(message: string, options?: BaseErrorOptions & { config?: string }) {
        const { config, ...baseOptions } = options ?? {}

        super(message, { ...baseOptions, name: 'ConfigurationError' })

        this.config = config
    }
}
