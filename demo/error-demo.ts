/* eslint-disable no-console */
import * as error from '../src/error'

// Demo 1: BaseError usage
console.log('=== BaseError Demo ===')

class CustomError extends error.BaseError {
    public constructor(message?: string, options?: error.BaseErrorOptions) {
        super(message, options)
    }
}

const customError = new CustomError('Something went wrong', {
    code: 'CUSTOM_001',
    retryable: true,
})

console.log('CustomError:', customError.toString())
console.log('CustomError JSON:', JSON.stringify(customError.toJSON(), null, 2))

// Demo 2: isAbortError usage
console.log('\n=== isAbortError Demo ===')

const abortError = new DOMException('Operation was aborted', 'AbortError')
const regularError = new Error('Regular error')

console.log('Is AbortError (AbortError):', error.isAbortError(abortError))
console.log('Is AbortError (Error):', error.isAbortError(regularError))

// Demo with AbortController
const controller = new AbortController()
controller.abort()

console.log('Is AbortError (AbortController reason):', error.isAbortError(controller.signal.reason))

// Demo 3: Error with cause
console.log('\n=== Error with Cause Demo ===')

const originalError = new Error('Original problem')
const wrappedError = new CustomError('Wrapped error', { cause: originalError })

console.log('Wrapped error:', wrappedError.message)
console.log('Original cause:', wrappedError.cause)
console.log('Wrapped error JSON:', JSON.stringify(wrappedError.toJSON(), null, 2))

console.log('\n=== Demo completed ===')
/* eslint-enable no-console */
