import { describe, expect, test } from 'vitest'
import { BaseError, type BaseErrorOptions } from '../../src/error/base-error'

class TestError extends BaseError {
    constructor(message?: string, options?: BaseErrorOptions) {
        super(message, options)
    }
}

describe('BaseError', () => {
    test('should create error with default values', () => {
        const error = new TestError('Test message')

        expect(error.message).toBe('Test message')
        expect(error.name).toBe('TestError')
        expect(error.timestamp).toBeInstanceOf(Date)
        expect(error.code).toBeUndefined()
        expect(error.retryable).toBeUndefined()
    })

    test('should create error with custom options', () => {
        const error = new TestError('Test message', {
            name: 'CustomError',
            code: 'E001',
            retryable: true,
        })

        expect(error.message).toBe('Test message')
        expect(error.name).toBe('CustomError')
        expect(error.code).toBe('E001')
        expect(error.retryable).toBe(true)
    })

    test('should create error with numeric code', () => {
        const error = new TestError('Test message', { code: 404 })

        expect(error.code).toBe(404)
    })

    test('should preserve error cause', () => {
        const cause = new Error('Original error')
        const error = new TestError('Test message', { cause })

        expect(error.cause).toBe(cause)
    })

    test('should convert to JSON correctly', () => {
        const error = new TestError('Test message', {
            code: 'E001',
            retryable: true,
        })

        const json = error.toJSON()

        expect(json.message).toBe('Test message')
        expect(json.name).toBe('TestError')
        expect(json.code).toBe('E001')
        expect(json.retryable).toBe(true)
        expect(json.timestamp).toBeDefined()
        expect(json.stack).toBeDefined()
    })

    test('should convert to JSON with nested error cause', () => {
        const cause = new Error('Original error')
        const error = new TestError('Test message', { cause })

        const json = error.toJSON()

        expect(json.cause).toBeDefined()
        expect(typeof json.cause).toBe('object')
    })

    test('should convert to string without code', () => {
        const error = new TestError('Test message')

        expect(error.toString()).toBe('TestError: Test message')
    })

    test('should convert to string with code', () => {
        const error = new TestError('Test message', { code: 'E001' })

        expect(error.toString()).toBe('[E001] TestError: Test message')
    })

    test('should have proper prototype chain', () => {
        const error = new TestError('Test message')

        expect(error).toBeInstanceOf(TestError)
        expect(error).toBeInstanceOf(BaseError)
        expect(error).toBeInstanceOf(Error)
    })

    test('should capture stack trace', () => {
        const error = new TestError('Test message')

        expect(error.stack).toBeDefined()
        expect(error.stack).toContain('TestError')
    })
})