import { describe, expect, test } from 'vitest'
import { AggregateError, createErrorWithCause, isErrorLike } from '../../src/error/errors'

describe('AggregateError', () => {
    test('should create with array of errors', () => {
        const errors = [
            new Error('First error'),
            new Error('Second error')
        ]
        const aggregateError = new AggregateError(errors)

        expect(aggregateError.name).toBe('AggregateError')
        expect(aggregateError.message).toBe('2 error(s) occurred')
        expect(aggregateError.errors).toEqual(errors)
    })

    test('should create with custom message', () => {
        const errors = [new Error('Test error')]
        const aggregateError = new AggregateError(errors, 'Multiple failures')

        expect(aggregateError.message).toBe('Multiple failures')
        expect(aggregateError.errors).toEqual(errors)
    })

    test('should handle empty errors array', () => {
        const aggregateError = new AggregateError([])

        expect(aggregateError.message).toBe('0 error(s) occurred')
        expect(aggregateError.errors).toEqual([])
    })
})

describe('createErrorWithCause', () => {
    test('should create error with cause', () => {
        const cause = new Error('Original error')
        const error = createErrorWithCause('Wrapped error', cause)

        expect(error.message).toBe('Wrapped error')
        expect(error.cause).toBe(cause)
    })

    test('should create error with non-error cause', () => {
        const cause = 'String cause'
        const error = createErrorWithCause('Wrapped error', cause)

        expect(error.message).toBe('Wrapped error')
        expect(error.cause).toBe(cause)
    })
})

describe('isErrorLike', () => {
    test('should return true for error-like objects', () => {
        const errorLike = { name: 'TestError', message: 'Test message' }

        expect(isErrorLike(errorLike)).toBe(true)
    })

    test('should return true for Error instances', () => {
        const error = new Error('Test error')

        expect(isErrorLike(error)).toBe(true)
    })

    test('should return false for objects missing name', () => {
        const notErrorLike = { message: 'Test message' }

        expect(isErrorLike(notErrorLike)).toBe(false)
    })

    test('should return false for objects missing message', () => {
        const notErrorLike = { name: 'TestError' }

        expect(isErrorLike(notErrorLike)).toBe(false)
    })

    test('should return false for null', () => {
        expect(isErrorLike(null)).toBe(false)
    })

    test('should return false for strings', () => {
        expect(isErrorLike('error')).toBe(false)
    })

    test('should return false for objects with wrong types', () => {
        const notErrorLike = { name: 123, message: 'Test' }

        expect(isErrorLike(notErrorLike)).toBe(false)
    })
})
