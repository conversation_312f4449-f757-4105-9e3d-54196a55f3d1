import { describe, expect, test } from 'vitest'
import { getErrorMessage, getErrorStack, isTimeoutError } from '../../src/error/errors'

describe('isTimeoutError', () => {
    test('should return true for TimeoutError', () => {
        const timeoutError = new Error('Operation timed out')
        timeoutError.name = 'TimeoutError'

        expect(isTimeoutError(timeoutError)).toBe(true)
    })

    test('should return true for error with timeout in message', () => {
        const error = new Error('Request timeout occurred')

        expect(isTimeoutError(error)).toBe(true)
    })

    test('should return false for regular error', () => {
        const error = new Error('Regular error')

        expect(isTimeoutError(error)).toBe(false)
    })
})

describe('getErrorMessage', () => {
    test('should return message from Error instance', () => {
        const error = new Error('Test error message')

        expect(getErrorMessage(error)).toBe('Test error message')
    })

    test('should return string if input is string', () => {
        expect(getErrorMessage('String error')).toBe('String error')
    })

    test('should return unknown error for null', () => {
        expect(getErrorMessage(null)).toBe('Unknown error')
    })
})

describe('getErrorStack', () => {
    test('should return stack from Error instance', () => {
        const error = new Error('Test error')

        expect(getErrorStack(error)).toBeDefined()
        expect(typeof getErrorStack(error)).toBe('string')
    })

    test('should return undefined for non-error values', () => {
        expect(getErrorStack(null)).toBeUndefined()
        expect(getErrorStack('error')).toBeUndefined()
    })
})
