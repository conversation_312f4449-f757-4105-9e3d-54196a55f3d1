import { describe, expect, test } from 'vitest'
import { ConfigurationError, NotFoundError, TimeoutError, ValidationError } from '../../src/error/concrete-errors'

describe('ValidationError', () => {
    test('should create with message only', () => {
        const error = new ValidationError('Invalid input')

        expect(error.message).toBe('Invalid input')
        expect(error.name).toBe('ValidationError')
        expect(error.field).toBeUndefined()
    })

    test('should create with field information', () => {
        const error = new ValidationError('Invalid email', { field: 'email' })

        expect(error.message).toBe('Invalid email')
        expect(error.name).toBe('ValidationError')
        expect(error.field).toBe('email')
    })

    test('should preserve other options', () => {
        const error = new ValidationError('Invalid input', {
            field: 'username',
            code: 'VALIDATION_FAILED',
            retryable: false
        })

        expect(error.field).toBe('username')
        expect(error.code).toBe('VALIDATION_FAILED')
        expect(error.retryable).toBe(false)
    })
})

describe('TimeoutError', () => {
    test('should create with timeout value', () => {
        const error = new TimeoutError('Operation timed out', 5000)

        expect(error.message).toBe('Operation timed out')
        expect(error.name).toBe('TimeoutError')
        expect(error.timeout).toBe(5000)
    })

    test('should preserve other options', () => {
        const error = new TimeoutError('Request timeout', 3000, {
            code: 'TIMEOUT',
            retryable: true
        })

        expect(error.timeout).toBe(3000)
        expect(error.code).toBe('TIMEOUT')
        expect(error.retryable).toBe(true)
    })
})

describe('NotFoundError', () => {
    test('should create with default 404 code', () => {
        const error = new NotFoundError('Resource not found')

        expect(error.message).toBe('Resource not found')
        expect(error.name).toBe('NotFoundError')
        expect(error.code).toBe(404)
        expect(error.resource).toBeUndefined()
    })

    test('should create with resource information', () => {
        const error = new NotFoundError('User not found', { resource: 'user' })

        expect(error.message).toBe('User not found')
        expect(error.resource).toBe('user')
        expect(error.code).toBe(404)
    })
})

describe('ConfigurationError', () => {
    test('should create with message only', () => {
        const error = new ConfigurationError('Invalid configuration')

        expect(error.message).toBe('Invalid configuration')
        expect(error.name).toBe('ConfigurationError')
        expect(error.config).toBeUndefined()
    })

    test('should create with config information', () => {
        const error = new ConfigurationError('Missing API key', { config: 'apiKey' })

        expect(error.message).toBe('Missing API key')
        expect(error.config).toBe('apiKey')
    })
})
