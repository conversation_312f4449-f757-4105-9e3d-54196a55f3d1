import { describe, expect, test } from 'vitest'
import { isAbortError } from '../../src/error/errors'

describe('isAbortError', () => {
    test('should return true for AbortError', () => {
        const abortError = new DOMException('Operation was aborted', 'AbortError')

        expect(isAbortError(abortError)).toBe(true)
    })

    test('should return false for regular Error', () => {
        const regularError = new Error('Regular error')

        expect(isAbortError(regularError)).toBe(false)
    })

    test('should return false for other DOMException types', () => {
        const otherError = new DOMException('Other error', 'NetworkError')

        expect(isAbortError(otherError)).toBe(false)
    })

    test('should return false for null', () => {
        expect(isAbortError(null)).toBe(false)
    })

    test('should return false for undefined', () => {
        expect(isAbortError(undefined)).toBe(false)
    })

    test('should return false for string', () => {
        expect(isAbortError('error')).toBe(false)
    })

    test('should return false for object with similar structure', () => {
        const fakeError = { name: 'AbortError', message: 'Fake abort error' }

        expect(isAbortError(fakeError)).toBe(false)
    })

    test('should work with AbortController signal', () => {
        const controller = new AbortController()
        controller.abort()

        const signal = controller.signal

        expect(signal.aborted).toBe(true)
        expect(signal.reason).toBeInstanceOf(DOMException)
        expect(isAbortError(signal.reason)).toBe(true)
    })
})